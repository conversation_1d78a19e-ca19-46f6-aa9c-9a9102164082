//
//  BookConfigModel.h
//  PPBrowser
//
//  Created by qingbin on 2025/6/3.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "BaseModel.h"

//简繁体切换
typedef NS_ENUM(NSInteger, LanguageType) {
    LanguageTypeAuto,
    LanguageTypeSimplifiedChinese,
    LanguageTypeTraditionalChinese
};

NS_ASSUME_NONNULL_BEGIN

@interface BookConfigModel : BaseModel
//字体大小
@property (nonatomic, assign) CGFloat fontSize;
//行间距
@property (nonatomic, assign) CGFloat lineSpace;
//段落间距
@property (nonatomic, assign) CGFloat paragraphSpace;
//首行缩进 0-0, 1-1个字的宽度, 2-2个字的宽度
@property (nonatomic, assign) CGFloat firstLineHeadIndent;
//左右间距
@property (nonatomic, assign) CGFloat margin;
//上边距
@property (nonatomic, assign) CGFloat topMargin;
//下边距
@property (nonatomic, assign) CGFloat bottomMargin;
//两端对齐
@property (nonatomic, assign) BOOL justifiedAlignment;
//单手模式
@property (nonatomic, assign) BOOL oneHandMode;
//简繁体切换
@property (nonatomic, assign) LanguageType selectedLanguage;

@end

NS_ASSUME_NONNULL_END
